// Executive Cost Savings Dashboard - Enhanced UX with Progressive Disclosure
// Soil Master v1.0.6 - Improved User Experience

'use client';

import React, { useState, useEffect } from 'react';
import {
  TrendingDown,
  DollarSign,
  Target,
  Award,
  BarChart3,
  PieChart,
  ArrowDown,
  CheckCircle,
  Zap,
  Calendar,
  Globe,
  Leaf,
  Calculator,
  Download,
  Share2,
  RefreshCw,
  TrendingUp,
  AlertCircle,
  Users
} from 'lucide-react';

// Import professional chart components
import {
  ROIProjection<PERSON><PERSON>,
  CostSavingsChart,
  PaybackPeriodChart,
  YieldImprovementChart,
  FinancialImpactChart
} from '@/components/charts/ChartComponents';

// Import UX components
import {
  CollapsibleSection,
  MetricCard
} from '@/components/ui/UXComponents';
import {
  DashboardLayout,
  RoleContent,
  QuickActionBar,
  Breadcrumb
} from '@/components/ui/DashboardLayout';

interface CostSavingsData {
  totalAnnualSavings: number;
  savingsPerHectare: number;
  fertilizerSavings: number;
  waterSavings: number;
  laborSavings: number;
  equipmentSavings: number;
  wasteReduction: number;
  yieldIncrease: number;
  revenueIncrease: number;
  paybackMonths: number;
  roi: number;
  npv: number;
}

interface ExecutiveMetric {
  title: string;
  value: string;
  change: number;
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<any>;
  color: string;
  description: string;
}

export default function ExecutiveCostSavingsDashboard() {
  const [timeframe, setTimeframe] = useState<'monthly' | 'quarterly' | 'annual'>('annual');
  const [farmSize, setFarmSize] = useState(1000);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [currentRole, setCurrentRole] = useState<'executive' | 'demonstration' | 'operational' | 'financial'>('executive');

  // Widget visibility state for customization
  const [widgets, setWidgets] = useState([
    { id: 'key-metrics', name: 'Key Cost Savings', visible: true, priority: 'high' as const },
    { id: 'breakdown', name: 'Savings Breakdown', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'charts', name: 'Visual Analytics', visible: currentRole === 'financial', priority: 'medium' as const },
    { id: 'sustainability', name: 'Sustainability Metrics', visible: false, priority: 'low' as const }
  ]);

  // Sample cost savings data
  const costSavingsData: CostSavingsData = {
    totalAnnualSavings: 285000,
    savingsPerHectare: 285,
    fertilizerSavings: 125000,
    waterSavings: 65000,
    laborSavings: 55000,
    equipmentSavings: 25000,
    wasteReduction: 15000,
    yieldIncrease: 22.5,
    revenueIncrease: 450000,
    paybackMonths: 28,
    roi: 31.8,
    npv: 1250000
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const executiveMetrics: ExecutiveMetric[] = [
    {
      title: 'Total Annual Savings',
      value: formatCurrency(costSavingsData.totalAnnualSavings),
      change: 18.5,
      trend: 'down',
      icon: TrendingDown,
      color: 'green',
      description: 'Operational cost reduction vs traditional farming'
    },
    {
      title: 'Revenue Increase',
      value: formatCurrency(costSavingsData.revenueIncrease),
      change: 22.5,
      trend: 'up',
      icon: TrendingUp,
      color: 'blue',
      description: 'Additional revenue from yield improvements'
    },
    {
      title: 'Return on Investment',
      value: formatPercent(costSavingsData.roi),
      change: 31.8,
      trend: 'up',
      icon: Target,
      color: 'purple',
      description: '5-year ROI with precision agriculture'
    },
    {
      title: 'Payback Period',
      value: `${Math.floor(costSavingsData.paybackMonths / 12)}.${costSavingsData.paybackMonths % 12} years`,
      change: -15.2,
      trend: 'down',
      icon: Calendar,
      color: 'orange',
      description: 'Time to recover initial investment'
    }
  ];

  const refreshData = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1500);
  };

  // Quick actions for the dashboard
  const quickActions = [
    {
      id: 'refresh',
      label: isLoading ? 'Refreshing...' : 'Refresh Data',
      icon: RefreshCw,
      onClick: refreshData,
      variant: 'primary' as const,
      disabled: isLoading
    },
    {
      id: 'download',
      label: 'Download Report',
      icon: Download,
      onClick: () => alert('Download functionality coming soon!'),
      variant: 'outline' as const
    },
    {
      id: 'share',
      label: 'Share Dashboard',
      icon: Share2,
      onClick: () => alert('Share functionality coming soon!'),
      variant: 'outline' as const
    }
  ];

  // Handle widget visibility changes
  const handleWidgetVisibilityChange = (widgetId: string, visible: boolean) => {
    setWidgets(prev => prev.map(widget =>
      widget.id === widgetId ? { ...widget, visible } : widget
    ));
  };

  return (
    <DashboardLayout
      title="Executive Cost Savings Dashboard"
      subtitle={`Real-time financial impact analysis • Last updated: ${lastUpdated.toLocaleTimeString()}`}
      currentRole={currentRole}
      onRoleChange={(role) => setCurrentRole(role as any)}
      availableRoles={['executive', 'demonstration', 'operational', 'financial']}
      widgets={widgets}
      onWidgetVisibilityChange={handleWidgetVisibilityChange}
      headerActions={<QuickActionBar actions={quickActions} />}
    >
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={[
        { label: 'Demo', href: '/demo' },
        { label: 'Cost Savings', current: true }
      ]} />

      {/* Farm Size Selector */}
      <div className="mb-6">
        <div className="flex items-center space-x-4 bg-white rounded-lg p-4 border border-gray-200">
          <label className="text-sm font-medium text-gray-700">Farm Size:</label>
          <select
            value={farmSize}
            onChange={(e) => setFarmSize(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value={500}>500 hectares</option>
            <option value={1000}>1,000 hectares</option>
            <option value={1500}>1,500 hectares</option>
            <option value={2000}>2,000 hectares</option>
          </select>
          <label className="text-sm font-medium text-gray-700">Timeframe:</label>
          <select
            value={timeframe}
            onChange={(e) => setTimeframe(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            <option value="monthly">Monthly</option>
            <option value="quarterly">Quarterly</option>
            <option value="annual">Annual</option>
          </select>
        </div>
      </div>

      {/* Role-Based Content */}
      <RoleContent
        role={currentRole}
        mode="overview"
      >
        {{
          executive: (
            <ExecutiveCostSavingsView
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          demonstration: (
            <DemonstrationCostSavingsView
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          operational: (
            <OperationalCostSavingsView
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          ),
          financial: (
            <FinancialCostSavingsView
              executiveMetrics={executiveMetrics}
              widgets={widgets}
              costSavingsData={costSavingsData}
              timeframe={timeframe}
              farmSize={farmSize}
            />
          )
        }}
      </RoleContent>
    </DashboardLayout>
  );
}

// Executive Cost Savings View - High-level KPIs only
const ExecutiveCostSavingsView: React.FC<{
  executiveMetrics: any[];
  widgets: any[];
  costSavingsData: CostSavingsData;
  timeframe: string;
  farmSize: number;
}> = ({ executiveMetrics, widgets, costSavingsData, timeframe, farmSize }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Key Executive Metrics Only */}
      {widgets.find(w => w.id === 'key-metrics')?.visible && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {executiveMetrics.slice(0, 4).map((metric, index) => {
            const Icon = metric.icon;
            const isPositive = metric.trend === 'up' || (metric.trend === 'down' && metric.title.includes('Savings'));

            return (
              <MetricCard
                key={index}
                title={metric.title}
                value={metric.value}
                change={metric.change}
                trend={metric.trend as any}
                priority="high"
                icon={Icon}
                color={metric.color}
                size="large"
                details={
                  <div className="text-sm text-gray-600">
                    <p>{metric.description}</p>
                  </div>
                }
              />
            );
          })}
        </div>
      )}

      {/* Executive Summary */}
      <CollapsibleSection
        title="Financial Impact Summary"
        defaultExpanded={true}
        priority="high"
        icon={Target}
      >
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">{formatCurrency(costSavingsData.totalAnnualSavings)}</div>
              <div className="text-sm text-gray-600">Total Annual Savings</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{costSavingsData.paybackMonths} months</div>
              <div className="text-sm text-gray-600">Investment Recovery</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">{costSavingsData.roi.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Return on Investment</div>
            </div>
          </div>
        </div>
      </CollapsibleSection>
    </div>
  );
};

// Demonstration Cost Savings View - Visual showcase
const DemonstrationCostSavingsView: React.FC<{
  executiveMetrics: any[];
  widgets: any[];
  costSavingsData: CostSavingsData;
  timeframe: string;
  farmSize: number;
}> = ({ executiveMetrics, widgets, costSavingsData, timeframe, farmSize }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Visual Cost Savings Showcase */}
      {widgets.find(w => w.id === 'key-metrics')?.visible && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg p-6">
            <div className="flex items-center mb-3">
              <DollarSign className="w-8 h-8 mr-3" />
              <h3 className="text-lg font-semibold">Total Savings</h3>
            </div>
            <div className="text-3xl font-bold">{formatCurrency(costSavingsData.totalAnnualSavings)}</div>
            <p className="text-green-100 text-sm">Annual cost reduction achieved</p>
          </div>

          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6">
            <div className="flex items-center mb-3">
              <TrendingDown className="w-8 h-8 mr-3" />
              <h3 className="text-lg font-semibold">Fertilizer Savings</h3>
            </div>
            <div className="text-3xl font-bold">{formatCurrency(costSavingsData.fertilizerSavings)}</div>
            <p className="text-blue-100 text-sm">Precision application reduces waste</p>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg p-6">
            <div className="flex items-center mb-3">
              <Zap className="w-8 h-8 mr-3" />
              <h3 className="text-lg font-semibold">Labor Efficiency</h3>
            </div>
            <div className="text-3xl font-bold">{formatCurrency(costSavingsData.laborSavings)}</div>
            <p className="text-purple-100 text-sm">Automated monitoring saves time</p>
          </div>

          <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg p-6">
            <div className="flex items-center mb-3">
              <Calendar className="w-8 h-8 mr-3" />
              <h3 className="text-lg font-semibold">Payback Period</h3>
            </div>
            <div className="text-3xl font-bold">{costSavingsData.paybackMonths} months</div>
            <p className="text-orange-100 text-sm">Fast return on investment</p>
          </div>
        </div>
      )}

      {/* Savings Breakdown */}
      {widgets.find(w => w.id === 'breakdown')?.visible && (
        <CollapsibleSection
          title="Cost Savings Breakdown"
          defaultExpanded={false}
          priority="medium"
          icon={BarChart3}
          badge="Detailed"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Annual Savings by Category</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Fertilizer Optimization:</span>
                  <span className="font-medium text-green-600">{formatCurrency(costSavingsData.fertilizerSavings)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Water Management:</span>
                  <span className="font-medium text-blue-600">{formatCurrency(costSavingsData.waterSavings)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Labor Efficiency:</span>
                  <span className="font-medium text-purple-600">{formatCurrency(costSavingsData.laborSavings)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Equipment Optimization:</span>
                  <span className="font-medium text-orange-600">{formatCurrency(costSavingsData.equipmentSavings)}</span>
                </div>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Environmental Impact</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Waste Reduction:</span>
                  <span className="font-medium text-green-600">{costSavingsData.wasteReduction}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Yield Increase:</span>
                  <span className="font-medium text-blue-600">{costSavingsData.yieldIncrease}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Revenue Increase:</span>
                  <span className="font-medium text-purple-600">{formatCurrency(costSavingsData.revenueIncrease)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">NPV (5-year):</span>
                  <span className="font-medium text-orange-600">{formatCurrency(costSavingsData.npv)}</span>
                </div>
              </div>
            </div>
          </div>
        </CollapsibleSection>
      )}
    </div>
  );
};

// Operational Cost Savings View - Implementation focused
const OperationalCostSavingsView: React.FC<{
  executiveMetrics: any[];
  widgets: any[];
  costSavingsData: CostSavingsData;
  timeframe: string;
  farmSize: number;
}> = ({ executiveMetrics, widgets, costSavingsData, timeframe, farmSize }) => {
  return (
    <div className="space-y-6">
      {/* Operational Metrics */}
      {widgets.find(w => w.id === 'key-metrics')?.visible && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <MetricCard
            title="Daily Savings"
            value={`RM ${(costSavingsData.totalAnnualSavings / 365).toFixed(0)}`}
            priority="high"
            icon={Calendar}
            color="green"
            size="large"
          />
          <MetricCard
            title="Efficiency Gain"
            value={`${costSavingsData.wasteReduction}%`}
            priority="high"
            icon={Zap}
            color="blue"
            size="large"
          />
          <MetricCard
            title="Resource Optimization"
            value="Active"
            priority="medium"
            icon={Target}
            color="purple"
            size="large"
          />
        </div>
      )}

      {/* Operational Insights */}
      <CollapsibleSection
        title="Operational Insights"
        defaultExpanded={true}
        priority="high"
        icon={Users}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Resource Optimization</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• Fertilizer usage reduced by 25% through precision application</li>
              <li>• Water consumption optimized with smart irrigation scheduling</li>
              <li>• Labor efficiency improved with automated monitoring alerts</li>
              <li>• Equipment utilization maximized through predictive maintenance</li>
            </ul>
          </div>
          <div className="bg-white border rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Performance Indicators</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• 98% sensor uptime and data accuracy</li>
              <li>• 15% reduction in manual field inspections</li>
              <li>• 30% faster response to crop stress indicators</li>
              <li>• 20% improvement in harvest timing precision</li>
            </ul>
          </div>
        </div>
      </CollapsibleSection>
    </div>
  );
};

// Financial Cost Savings View - Detailed financial analysis
const FinancialCostSavingsView: React.FC<{
  executiveMetrics: any[];
  widgets: any[];
  costSavingsData: CostSavingsData;
  timeframe: string;
  farmSize: number;
}> = ({ executiveMetrics, widgets, costSavingsData, timeframe, farmSize }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-MY', {
      style: 'currency',
      currency: 'MYR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Comprehensive Financial Metrics */}
      {widgets.find(w => w.id === 'key-metrics')?.visible && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {executiveMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <MetricCard
                key={index}
                title={metric.title}
                value={metric.value}
                change={metric.change}
                trend={metric.trend as any}
                priority="high"
                icon={Icon}
                color={metric.color}
                details={
                  <div className="text-sm text-gray-600">
                    <p>{metric.description}</p>
                  </div>
                }
              />
            );
          })}
        </div>
      )}

      {/* Detailed Financial Analysis */}
      {widgets.find(w => w.id === 'charts')?.visible && (
        <CollapsibleSection
          title="Financial Analysis & Projections"
          defaultExpanded={true}
          priority="high"
          icon={BarChart3}
          badge="Comprehensive"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white border rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">5-Year Financial Projection</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Year 1 Savings:</span>
                  <span className="font-medium">{formatCurrency(costSavingsData.totalAnnualSavings)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Year 3 Cumulative:</span>
                  <span className="font-medium">{formatCurrency(costSavingsData.totalAnnualSavings * 3)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Year 5 Cumulative:</span>
                  <span className="font-medium">{formatCurrency(costSavingsData.totalAnnualSavings * 5)}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-semibold">
                  <span>NPV (10% discount):</span>
                  <span className="text-green-600">{formatCurrency(costSavingsData.npv)}</span>
                </div>
              </div>
            </div>

            <div className="bg-white border rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-3">Risk Assessment</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Technology Risk:</span>
                  <span className="font-medium text-green-600">Low</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Market Risk:</span>
                  <span className="font-medium text-yellow-600">Medium</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Implementation Risk:</span>
                  <span className="font-medium text-green-600">Low</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-semibold">
                  <span>Overall Risk Score:</span>
                  <span className="text-green-600">Low-Medium</span>
                </div>
              </div>
            </div>
          </div>
        </CollapsibleSection>
      )}
    </div>
  );
};
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {executiveMetrics.map((metric, index) => {
            const Icon = metric.icon;
            const isPositive = metric.trend === 'up' || (metric.trend === 'down' && metric.title.includes('Savings'));
            
            return (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6 border-l-4 border-green-500">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 bg-${metric.color}-100 rounded-lg flex items-center justify-center`}>
                    <Icon className={`w-6 h-6 text-${metric.color}-600`} />
                  </div>
                  <div className={`flex items-center text-sm font-medium ${
                    isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {isPositive ? '↗' : '↘'} {Math.abs(metric.change)}%
                  </div>
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</div>
                <div className="text-sm font-medium text-gray-700 mb-2">{metric.title}</div>
                <div className="text-xs text-gray-500">{metric.description}</div>
              </div>
            );
          })}
        </div>

        {/* Cost Savings Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Cost Savings Breakdown</h3>
              <div className="text-sm text-gray-500">Annual Savings: {formatCurrency(costSavingsData.totalAnnualSavings)}</div>
            </div>
            <CostSavingsChart
              data={{
                categories: ['Fertilizer Optimization', 'Water Management', 'Labor Efficiency', 'Equipment Maintenance', 'Waste Reduction'],
                savings: [
                  costSavingsData.fertilizerSavings,
                  costSavingsData.waterSavings,
                  costSavingsData.laborSavings,
                  costSavingsData.equipmentSavings,
                  costSavingsData.wasteReduction
                ]
              }}
            />
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Investment Recovery</h3>
              <div className="text-sm text-gray-500">Payback: {Math.floor(costSavingsData.paybackMonths / 12)}.{costSavingsData.paybackMonths % 12} years</div>
            </div>
            <PaybackPeriodChart
              data={{
                investmentRecovered: 65,
                remainingInvestment: 35,
                paybackMonths: costSavingsData.paybackMonths
              }}
            />
          </div>
        </div>

        {/* Financial Impact Over Time */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">5-Year Financial Impact Projection</h3>
            <div className="flex space-x-2">
              {['monthly', 'quarterly', 'annual'].map((period) => (
                <button
                  key={period}
                  onClick={() => setTimeframe(period as any)}
                  className={`px-3 py-1 text-sm rounded ${
                    timeframe === period
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {period.charAt(0).toUpperCase() + period.slice(1)}
                </button>
              ))}
            </div>
          </div>
          <ROIProjectionChart
            data={{
              years: ['Year 0', 'Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
              traditional: [0, 0, 0, 0, 0, 0],
              precision: [-750000, -465000, -180000, 105000, 390000, 675000]
            }}
          />
        </div>

        {/* Key Performance Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Leaf className="w-8 h-8 text-green-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Sustainability Impact</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Fertilizer Reduction:</span>
                <span className="font-medium text-green-600">18%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Water Conservation:</span>
                <span className="font-medium text-blue-600">22%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Carbon Footprint:</span>
                <span className="font-medium text-purple-600">-15%</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Users className="w-8 h-8 text-blue-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Operational Efficiency</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Labor Productivity:</span>
                <span className="font-medium text-green-600">+25%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Decision Speed:</span>
                <span className="font-medium text-blue-600">+40%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Error Reduction:</span>
                <span className="font-medium text-purple-600">-35%</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Globe className="w-8 h-8 text-purple-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Market Position</h3>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Industry Ranking:</span>
                <span className="font-medium text-green-600">Top 15%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Competitive Advantage:</span>
                <span className="font-medium text-blue-600">High</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Market Share Growth:</span>
                <span className="font-medium text-purple-600">+12%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Executive Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">Executive Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="flex items-center justify-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <Download className="w-5 h-5 mr-2" />
              Download Executive Report
            </button>
            <button className="flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Share2 className="w-5 h-5 mr-2" />
              Share with Board
            </button>
            <button className="flex items-center justify-center px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <Calculator className="w-5 h-5 mr-2" />
              Detailed Analysis
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
