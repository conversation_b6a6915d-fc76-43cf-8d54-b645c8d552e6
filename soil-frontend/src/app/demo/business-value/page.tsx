// Business Value Demonstration Hub - Enhanced UX with Progressive Disclosure
// Soil Master v1.0.6 - Improved User Experience

'use client';

import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  DollarSign, 
  Target, 
  Award,
  BarChart3,
  CheckCircle,
  Zap,
  Users,
  Calculator,
  Download,
  Share2,
  Play,
  Clock,
  Shield
} from 'lucide-react';

// Import UX components
import {
  CollapsibleSection,
  MetricCard
} from '@/components/ui/UXComponents';
import {
  DashboardLayout,
  RoleContent,
  QuickActionBar,
  Breadcrumb
} from '@/components/ui/DashboardLayout';

export default function BusinessValueDemonstrationPage() {
  const [activeDemo, setActiveDemo] = useState<'overview' | 'cases' | 'stories' | 'calculator'>('overview');
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentRole, setCurrentRole] = useState<'executive' | 'demonstration' | 'operational' | 'financial'>('demonstration');

  // Widget visibility state for customization
  const [widgets, setWidgets] = useState([
    { id: 'key-metrics', name: 'Key Value Propositions', visible: true, priority: 'high' as const },
    { id: 'business-cases', name: 'Business Cases', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'success-stories', name: 'Success Stories', visible: currentRole !== 'executive', priority: 'medium' as const },
    { id: 'charts', name: 'Financial Charts', visible: currentRole === 'financial', priority: 'low' as const }
  ]);

  const startDemo = () => {
    setIsPlaying(true);
    setTimeout(() => setIsPlaying(false), 3000);
  };

  // Quick actions for the dashboard
  const quickActions = [
    {
      id: 'demo',
      label: isPlaying ? 'Demo Running...' : 'Start Demo',
      icon: isPlaying ? Clock : Play,
      onClick: startDemo,
      variant: 'primary' as const,
      disabled: isPlaying
    },
    {
      id: 'download',
      label: 'Download Report',
      icon: Download,
      onClick: () => alert('Download functionality coming soon!'),
      variant: 'outline' as const
    },
    {
      id: 'share',
      label: 'Share Analysis',
      icon: Share2,
      onClick: () => alert('Share functionality coming soon!'),
      variant: 'outline' as const
    }
  ];

  // Handle widget visibility changes
  const handleWidgetVisibilityChange = (widgetId: string, visible: boolean) => {
    setWidgets(prev => prev.map(widget => 
      widget.id === widgetId ? { ...widget, visible } : widget
    ));
  };

  return (
    <DashboardLayout
      title="Business Value Demonstration"
      subtitle="Quantify and visualize the clear ROI and business impact of precision agriculture"
      currentRole={currentRole}
      onRoleChange={(role) => setCurrentRole(role as any)}
      availableRoles={['executive', 'demonstration', 'operational', 'financial']}
      widgets={widgets}
      onWidgetVisibilityChange={handleWidgetVisibilityChange}
      headerActions={<QuickActionBar actions={quickActions} />}
    >
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={[
        { label: 'Demo', href: '/demo' },
        { label: 'Business Value', current: true }
      ]} />

      {/* Tab Navigation - Simplified */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {[
            { id: 'overview', name: 'Value Overview', icon: Target },
            { id: 'cases', name: 'Business Cases', icon: BarChart3 },
            { id: 'stories', name: 'Success Stories', icon: Award },
            { id: 'calculator', name: 'ROI Calculator', icon: Calculator }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveDemo(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                  activeDemo === tab.id
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Role-Based Content */}
      <RoleContent
        role={currentRole}
        mode="overview"
      >
        {{
          executive: (
            <ExecutiveBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          demonstration: (
            <DemonstrationBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          operational: (
            <OperationalBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          ),
          financial: (
            <FinancialBusinessValueView 
              activeDemo={activeDemo}
              widgets={widgets}
            />
          )
        }}
      </RoleContent>
    </DashboardLayout>
  );
}

// Executive Business Value View - High-level value propositions only
const ExecutiveBusinessValueView: React.FC<{
  activeDemo: string;
  widgets: any[];
}> = ({ activeDemo, widgets }) => {
  if (activeDemo === 'overview') {
    return (
      <div className="space-y-6">
        {/* Key Value Propositions Only */}
        {widgets.find(w => w.id === 'key-metrics')?.visible && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <MetricCard
              title="Cost Reduction"
              value="15-30%"
              priority="high"
              icon={DollarSign}
              color="green"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Reduce fertilizer and operational costs through precision agriculture</p>
                </div>
              }
            />
            <MetricCard
              title="Yield Increase"
              value="20-35%"
              priority="high"
              icon={TrendingUp}
              color="blue"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Optimize growing conditions for maximum crop productivity</p>
                </div>
              }
            />
            <MetricCard
              title="Payback Period"
              value="2-4 Years"
              priority="high"
              icon={Clock}
              color="purple"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Fast return on investment with proven results</p>
                </div>
              }
            />
            <MetricCard
              title="Compliance"
              value="MPOB Ready"
              priority="medium"
              icon={CheckCircle}
              color="orange"
              size="large"
              details={
                <div className="text-sm text-gray-600">
                  <p>Meet MPOB standards and environmental compliance</p>
                </div>
              }
            />
          </div>
        )}

        {/* Executive Summary */}
        <CollapsibleSection
          title="Executive Summary"
          defaultExpanded={true}
          priority="high"
          icon={Target}
        >
          <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Strategic Business Impact</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Financial Benefits</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Immediate cost reduction through optimized resource usage</li>
                  <li>• Increased revenue from higher yields and quality</li>
                  <li>• Predictable ROI with 2-4 year payback period</li>
                  <li>• Reduced operational risks and waste</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Strategic Advantages</h4>
                <ul className="space-y-1 text-sm text-gray-600">
                  <li>• Market leadership in sustainable agriculture</li>
                  <li>• Compliance with environmental regulations</li>
                  <li>• Enhanced brand reputation and ESG credentials</li>
                  <li>• Future-ready technology infrastructure</li>
                </ul>
              </div>
            </div>
          </div>
        </CollapsibleSection>
      </div>
    );
  }

  return <div className="text-center py-12 text-gray-500">Select Value Overview to view executive summary</div>;
};
